import google.generativeai as genai
import os
from dotenv import load_dotenv

load_dotenv()

def main():
    """
    一个简单的、基于命令行的Gemini API对话程序。
    """
    try:
        # 从环境变量中获取API密钥并配置。
        # 为了安全，不建议直接在代码中写入密钥。
        api_key = os.getenv("GEMINI_API_KEY")
        if not api_key:
            print("错误：请先设置您的 GEMINI_API_KEY 环境变量。")
            print("获取密钥地址: https://aistudio.google.com/app/apikey")
            return
            
        genai.configure(api_key=api_key)

        # 创建一个生成模型实例
        model = genai.GenerativeModel('gemini-pro')

        # 使用 start_chat() 来创建一个可以持续多轮对话的会话。
        # history 参数可以用来初始化对话历史。
        chat = model.start_chat(history=[])

        print("Gemini 大模型对话程序已启动！")
        print("输入 'quit' 或 'exit' 来结束对话。")
        print("-" * 30)

        while True:
            # 获取用户输入
            user_input = input("你: ")

            # 检查退出条件
            if user_input.lower() in ['quit', 'exit']:
                print("\n对话结束。")
                break

            if not user_input:
                continue

            # 将用户输入发送给模型
            # stream=True 表示以流式方式接收响应，可以更快地看到输出
            response = chat.send_message(user_input, stream=True)

            # 流式打印模型的响应
            print("Gemini: ", end="")
            for chunk in response:
                print(chunk.text, end="", flush=True)
            print() # 在每次响应结束后换行

    except Exception as e:
        print(f"\n程序运行出错: {e}")

if __name__ == "__main__":
    main()